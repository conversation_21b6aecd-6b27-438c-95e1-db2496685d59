import Blog from "../models/blogModel.js";
import * as factory from "./handlerFactory.js";
import { subscriptionMail } from "../utils/emailTemplates/subscriptionMail.js";
import { newBlogMail } from "../utils/emailTemplates/templates.js";

// Create a new blog
export const createBlog = factory.createOne(Blog, (blog)=>subscriptionMail(newBlogMail(blog)));
// Get all blogs
export const getAllBlogs = factory.getAll("", Blog, [{ path: "category" }]);
// Get a blog by ID
export const getBlogById = factory.getOne(Blog, "category");
// Update a blog by ID
export const updateBlog = factory.updateOne(Blog);
// Delete a blog by ID
export const deleteBlog = factory.deleteOne(Blog);

export const getAllFilters = async (req, res) => {
  try {
    const blogs = await Blog.find().populate("category"); // Fetch category details

    // Extract unique categories with both id and name
    const categoriesMap = new Map();
    blogs.forEach((blog) => {
      if (blog.category) {
        categoriesMap.set(blog.category._id.toString(), {
          categoryId: blog.category._id,
          categoryName: blog.category.categoryName,
        });
      }
    });

    // Convert map values to an array
    const categories = Array.from(categoriesMap.values());

    // Extract unique tags
    const tags = [...new Set(blogs.flatMap((blog) => blog.tags))];

    res.status(200).json({
      success: true,
      filters: {
        categories,
        tags,
      },
    });
  } catch (error) {
    res.status(500).json({ success: false, message: error.message });
  }
};
