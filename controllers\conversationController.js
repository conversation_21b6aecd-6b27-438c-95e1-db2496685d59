import Conversation from "../models/conversationModel.js";

export const createConversation = async (req, res) => {
  try {
    const { offerId, sellerId, type } = req.body;
    const clientId = req.user._id;

    const existing = await Conversation.findOne({
      offer: offerId,
      client: clientId,
      seller: sellerId,
    });

    if (existing) return res.json(existing);

    const conversation = new Conversation({
      offer: offerId,
      client: clientId,
      seller: sellerId,
      type,
    });

    await conversation.save();
    res.status(201).json(conversation);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

export const getConversations = async (req, res) => {
  try {
    const userId = req.user.id;

    let conversations = await Conversation.find({
      $or: [{ client: userId }, { seller: userId }],
      archived: false,
    })
        .populate('offer')
        .populate('client')
        .populate('seller')
        .sort('-updatedAt');

    conversations = conversations.filter(c => c.type === 'offer');

    res.json(conversations);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};

export const getConversation = async (req, res) => {
  try {
    const conversation = await Conversation.findById(req.params.id)
      .populate("offer")
      .populate("client")
      .populate("seller");

    if (!conversation) return res.status(404).json({ error: "Not found" });

    // Verify user is part of conversation

    res.json(conversation);
  } catch (err) {
    res.status(500).json({ error: err.message });
  }
};
