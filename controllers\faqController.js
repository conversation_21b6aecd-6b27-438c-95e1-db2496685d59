import FAQ from "../models/faqModel.js";
import * as factory from "./handlerFactory.js";

// Create FAQ
export const createFAQ = factory.createOne(FAQ);
// Get single FAQ by ID
export const getFAQ = factory.getOne(FAQ);
// Get all FAQs (sorted by order)
export const getAllFAQs = factory.getAll("", FAQ);
// Update FAQ
export const updateFAQ = factory.updateOne(FAQ);
// Delete FAQ
export const deleteFAQ = factory.deleteOne(FAQ);
