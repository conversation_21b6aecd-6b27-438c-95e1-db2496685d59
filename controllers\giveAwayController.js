import GiveAwayModel from "../models/giveAwayModel.js";

export const getAllGiveaways = async (req, res) => {
    try {
        const giveaways = await GiveAwayModel.find().sort({ createdAt: -1 });
        res.status(200).json({data: giveaways});
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

export const getGiveawayById = async (req, res) => {
    try {
        const giveaway = await GiveAwayModel.findById(req.params.id);
        if (!giveaway) {
            return res.status(404).json({ message: 'Giveaway not found' });
        }
        res.status(200).json({data:giveaway});
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};

export const createGiveaway = async (req, res) => {
    try {
        const giveaway = await GiveAwayModel.create(req.body);
        res.status(201).json(giveaway);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

export const updateGiveaway = async (req, res) => {
    try {
        const giveaway = await GiveAwayModel.findByIdAndUpdate(req.params.id, req.body, { new: true });
        if (!giveaway) {
            return res.status(404).json({ message: 'Giveaway not found' });
        }
        res.status(200).json(giveaway);
    } catch (error) {
        res.status(400).json({ message: error.message });
    }
};

export const deleteGiveaway = async (req, res) => {
    try {
        const giveaway = await GiveAwayModel.findByIdAndDelete(req.params.id);
        if (!giveaway) {
            return res.status(404).json({ message: 'Giveaway not found' });
        }
        res.status(200).json({ message: 'Giveaway deleted successfully' });
    } catch (error) {
        res.status(500).json({ message: error.message });
    }
};
