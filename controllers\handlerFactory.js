import catchAsync from "./../utils/catchAsync.js";
import AppError from "./../utils/appError.js";
import APIFeatures from "./../utils/apiFeatures.js";
import Cart from "../models/cartModel.js";
import Shop from "../models/shopModel.js";
import Wishlist from "../models/wishListModel.js";

export const deleteOne = (Model) =>
  catchAsync(async (req, res, next) => {
    const doc = await Model.findByIdAndDelete(req.params.id);

    if (!doc) {
      return next(new AppError("No document found with that ID", 404));
    }

    res.status(200).json({
      status: "success",
      data: null,
    });
  });

export const updateOne = (Model) =>
  catchAsync(async (req, res, next) => {
    const doc = await Model.findByIdAndUpdate(req.params.id, req.body, {
      new: true,
      runValidators: true,
    });

    if (!doc) {
      return next(new AppError("No document found with that ID", 404));
    }

    res.status(200).json({
      status: "success",
      data: doc,
    });
  });

export const createOne = (Model, callback) =>
  catchAsync(async (req, res) => {
    const doc = await Model.create(req.body);
    callback?.(doc);
    res.status(201).json({
      status: "success",
      data: doc,
    });
  });

// export const getOne = (Model, popOptions) =>
//   catchAsync(async (req, res, next) => {
//     let query = Model.findById(req.params.id);
//     if (popOptions) query = query.populate(popOptions);
//     const doc = await query;

//     if (!doc) {
//       return next(new AppError("No document found with that ID", 404));
//     }

//     res.status(200).json({
//       status: "success",
//       data: {
//         data: doc,
//       },
//     });
//   });
export const getOne = (Model, popOptions) =>
  catchAsync(async (req, res, next) => {
    const query = Model.findById(req.params.id);

    // Apply population if options are provided
    if (popOptions) {
      Array.isArray(popOptions)
        ? popOptions.forEach((option) => query.populate(option))
        : query.populate(popOptions);
    }
    const doc = await query;

    if (!doc) {
      return next(new AppError("No document found with that ID", 404));
    }

    res.status(200).json({
      status: "success",
      data: doc,
    });
  });
//related offer should have limited items upto 3
//delivery time and part of the cart
//lowest price we need as well
//---
// export const getOneOffer = (Model, popOptions) =>
//   catchAsync(async (req, res, next) => {
//     const query = Model.findById(req.params.id);

//     // Apply population if options are provided
//     if (popOptions) {
//       Array.isArray(popOptions)
//         ? popOptions.forEach((option) => query.populate(option))
//         : query.populate(popOptions);
//     }

//     const doc = await query;

//     if (!doc) {
//       return next(new AppError("No document found with that ID", 404));
//     }

//     // Initialize variable to hold related offers
//     let relatedOffers = [];

//     // Check if the current model is 'Offer' to fetch related offers
//     if (Model.modelName === "Offer") {
//       // Query to find offers with the same template, excluding the current one
//       let relatedQuery = Model.find({
//         template: doc.template,
//         _id: { $ne: doc._id }, // Exclude the current offer
//       }).sort({ customerPays: 1 });

//       // Apply the same population options to the related offers query
//       if (popOptions) {
//         if (Array.isArray(popOptions)) {
//           popOptions.forEach((option) => relatedQuery.populate(option));
//         } else {
//           relatedQuery.populate(popOptions);
//         }
//       }

//       // Execute the query to get related offers
//       relatedOffers = await relatedQuery;
//     }

//     // Prepare the response object
//     const responseData = {
//       status: "success",
//       data: doc,
//     };

//     // Add related offers to the response if applicable
//     if (Model.modelName === "Offer") {
//       responseData.relatedOffers = relatedOffers;
//     }

//     res.status(200).json(responseData);
//   });
// export const getOneOffer = (Model, popOptions) =>
//   catchAsync(async (req, res, next) => {
//     const query = Model.findById(req.params.id);

//     // Apply population if options are provided
//     if (popOptions) {
//       Array.isArray(popOptions)
//         ? popOptions.forEach((option) => query.populate(option))
//         : query.populate(popOptions);
//     }

//     const doc = await query;

//     if (!doc) {
//       return next(new AppError("No document found with that ID", 404));
//     }

//     // Initialize related offers array
//     let relatedOffers = [];

//     // Fetch related offers if the model is 'Offer'
//     if (Model.modelName === "Offer") {
//       let relatedQuery = Model.find({
//         template: doc.template,
//         _id: { $ne: doc._id }, // Exclude the current offer
//       })
//         .sort({ customerPays: 1 }) // Sort by lowest price
//         .limit(3); // Limit to 3 offers

//       // Apply population to related offers if needed
//       if (popOptions) {
//         if (Array.isArray(popOptions)) {
//           popOptions.forEach((option) => relatedQuery.populate(option));
//         } else {
//           relatedQuery.populate(popOptions);
//         }
//       }

//       relatedOffers = await relatedQuery;
//     }

//     // Get userId from request
//     const userId = req.user?.id;

//     // Fetch cart if user is logged in
//     let cart = null;
//     if (userId) {
//       cart = await Cart.findOne({ user: userId }).populate("items.offer");
//     }

//     // Create a set of offer IDs in the cart for fast lookup
//     const cartOfferIds = new Set(
//       cart ? cart.items.map((item) => item.offer._id.toString()) : []
//     );

//     // Add `inCart` property to related offers
//     relatedOffers = relatedOffers.map((offer) => ({
//       ...offer._doc,
//       inCart: cartOfferIds.has(offer._id.toString()), // Mark if offer is in cart
//     }));

//     // Find the lowest `customerPays` price among the related offers
//     const lowestCustomerPays = relatedOffers.length
//       ? Math.min(...relatedOffers.map((offer) => offer.customerPays))
//       : null;

//     // Prepare response data
//     const responseData = {
//       status: "success",
//       data: doc,
//       relatedOffers,
//       lowestCustomerPays,
//     };

//     res.status(200).json(responseData);
//   });
// export const getOneOffer = (Model, popOptions) =>
//   catchAsync(async (req, res, next) => {
//     const query = Model.findById(req.params.id);

//     // Apply population if options are provided
//     if (popOptions) {
//       Array.isArray(popOptions)
//         ? popOptions.forEach((option) => query.populate(option))
//         : query.populate(popOptions);
//     }

//     const doc = await query;

//     if (!doc) {
//       return next(new AppError("No document found with that ID", 404));
//     }
//     const shop = await Shop.findOne({ seller: doc.seller._id });

//     // Initialize related offers array
//     let relatedOffers = [];

//     // Fetch related offers if the model is 'Offer'
//     if (Model.modelName === "Offer") {
//       let allRelatedOffers = await Model.find({
//         template: doc.template,
//         _id: { $ne: doc._id }, // Exclude the current offer
//       }).populate([{ path: "template" }, { path: "seller" }]);

//       // Sort related offers by `customerPays` (lowest first)
//       allRelatedOffers.sort((a, b) => a.customerPays - b.customerPays);

//       // Select only the 3 lowest-priced offers
//       relatedOffers = allRelatedOffers.slice(0, 3);
//     }

//     // Get userId from request
//     const userId = req.query?.userId;

//     // Fetch cart if user is logged in
//     let cart = null;
//     if (userId) {
//       cart = await Cart.findOne({ user: userId }).populate("items.offer");

//       // Create a set of offer IDs in the cart for fast lookup
//       const cartOfferIds = new Set(
//         cart ? cart.items.map((item) => item.offer._id.toString()) : []
//       );

//       // Add `inCart` property to related offers
//       relatedOffers = relatedOffers.map((offer) => ({
//         ...offer._doc,
//         inCart: cartOfferIds.has(offer._id.toString()), // Mark if offer is in cart
//       }));
//     }
//     // Find the lowest `customerPays` price among the **all** related offers (before slicing)
//     const lowestCustomerPays = relatedOffers.length
//       ? Math.min(...relatedOffers.map((offer) => offer.customerPays))
//       : null;

//     // Prepare response data
//     const responseData = {
//       status: "success",
//       data: doc,
//       shop: shop || null, // Include shop details in the response
//       relatedOffers,
//       lowestCustomerPays,
//     };

//     res.status(200).json(responseData);
//   });

// export const getOneOffer = (Model, popOptions) =>
//   catchAsync(async (req, res, next) => {
//     let query = Model.findById(req.params.id).populate("seller");

//     // Apply additional population if needed
//     if (popOptions) {
//       Array.isArray(popOptions)
//         ? popOptions.forEach((option) => query.populate(option))
//         : query.populate(popOptions);
//     }

//     const doc = await query;

//     if (!doc) {
//       return next(new AppError("No document found with that ID", 404));
//     }

//     // Fetch shop details separately
//     const shop = await Shop.findOne({ seller: doc.seller._id });

//     // Initialize related offers array
//     let relatedOffers = [];

//     if (Model.modelName === "Offer") {
//       let allRelatedOffers = await Model.find({
//         template: doc.template,
//         _id: { $ne: doc._id },
//       }).populate("seller");

//       // Sort related offers by `customerPays`
//       allRelatedOffers.sort((a, b) => a.customerPays - b.customerPays);

//       // Select only the 3 lowest-priced offers
//       relatedOffers = allRelatedOffers.slice(0, 3);

//       // Fetch shops for related offers
//       for (let offer of relatedOffers) {
//         offer.shop = await Shop.findOne({ seller: offer.seller._id });
//       }
//     }

//     // Get userId from request
//     const userId = req.query?.userId;

//     // Fetch cart if user is logged in
//     let cart = null;
//     if (userId) {
//       cart = await Cart.findOne({ user: userId }).populate("items.offer");

//       // Create a set of offer IDs in the cart for fast lookup
//       const cartOfferIds = new Set(
//         cart ? cart.items.map((item) => item.offer._id.toString()) : []
//       );

//       // Add `inCart` property to related offers
//       relatedOffers = relatedOffers.map((offer) => ({
//         ...offer._doc,
//         inCart: cartOfferIds.has(offer._id.toString()),
//       }));
//     }

//     // Find the lowest `customerPays` price among all related offers
//     const lowestCustomerPays = relatedOffers.length
//       ? Math.min(...relatedOffers.map((offer) => offer.customerPays))
//       : null;

//     // Prepare response data
//     const responseData = {
//       status: "success",
//       data: {
//         ...doc._doc,
//         shop, // Include shop details
//       },
//       relatedOffers,
//       lowestCustomerPays,
//     };

//     res.status(200).json(responseData);
//   });

export const getOneOffer = (Model, popOptions) =>
  catchAsync(async (req, res, next) => {
    let query = Model.findById(req.params.id).populate("seller");

    // Apply additional population if needed
    if (popOptions) {
      Array.isArray(popOptions)
        ? popOptions.forEach((option) => query.populate(option))
        : query.populate(popOptions);
    }

    const doc = await query;

    if (!doc) {
      return next(new AppError("No document found with that ID", 404));
    }

    // Fetch shop details separately
    const shop = await Shop.findOne({ seller: doc.seller._id });

    // Initialize related offers array
    let relatedOffers = [];

    if (Model.modelName === "Offer") {
      let allRelatedOffers = await Model.find({
        template: doc.template,
        _id: { $ne: doc._id },
      }).populate("seller");

      // Sort related offers by `customerPays`
      allRelatedOffers.sort((a, b) => a.customerPays - b.customerPays);

      // Select only the 3 lowest-priced offers
      relatedOffers = allRelatedOffers.slice(0, 3);

      // Fetch shops for related offers
      for (let offer of relatedOffers) {
        offer.shop = await Shop.findOne({ seller: offer.seller._id });
      }
    }

    // Get userId from request
    const userId = req.query?.userId;

    // Fetch cart if user is logged in
    let cart = null;
    if (userId) {
      cart = await Cart.findOne({ user: userId }).populate("items.offer");

      // Create a set of offer IDs in the cart for fast lookup
      const cartOfferIds = new Set(
        cart ? cart.items.map((item) => item.offer._id.toString()) : [],
      );

      // Add `inCart` property to related offers
      relatedOffers = relatedOffers.map((offer) => ({
        ...offer._doc,
        inCart: cartOfferIds.has(offer._id.toString()),
      }));
    }

    // Find the lowest `customerPays` price among all related offers
    const lowestCustomerPays = relatedOffers.length
      ? Math.min(...relatedOffers.map((offer) => offer.customerPays))
      : null;

    // Calculate average rating and total ratings for the offer
    const totalRatings = doc.ratings.length;
    const averageRating =
      totalRatings > 0
        ? doc.ratings.reduce((sum, rating) => sum + rating.stars, 0) /
          totalRatings
        : 0;

    // Prepare response data
    const responseData = {
      status: "success",
      data: {
        ...doc._doc,
        shop, // Include shop details
        totalRatings, // Total number of ratings
        averageRating: parseFloat(averageRating.toFixed(2)), // Average rating rounded to 2 decimal places
      },
      relatedOffers,
      lowestCustomerPays,
    };

    res.status(200).json(responseData);
  });
// export const getAll = (query, Model, populateOptions, key) =>
//   catchAsync(async (req, res, next) => {
//     const userId = req?.query?.userId; // Assuming user is available in the request object
//     let cart;
//     if (query === "offer" && userId) {
//       cart = await Cart.findOne({ user: userId }).populate("items.offer");
//     }
//     let features;
//     if (req.query.genres || req.query.listingType) {
//       features = new APIFeatures(Model.find(), req.query, Model)
//         .search(key)
//         .populate(populateOptions)
//         .advancefilter()
//         .sort()
//         .paginate();
//     } else {
//       features = new APIFeatures(Model.find(), req.query, Model)
//         .search(key)
//         .populate(populateOptions)
//         .filter()
//         .sort()
//         .paginate();
//     }

//     // const doc = await features.query.explain();
//     // const total = await Model.countDocuments(features.query || {});
//     // console.log(`Total: ${total}`);
//     const doc = await features.query;
//     console.log(doc.length);
//     // const total = await Model.countDocuments();
//     // console.log(req.query);
//     let pagination = {};
//     if (doc.length > 0) {
//       const currentPage = parseInt(features.query.page, 10) || 1; // Ensure the page is an integer
//       const limit = parseInt(features.query.limit, 10) || 10; // Default to 10 if no limit is provided
//       console.log(`Current Page: ${currentPage}`);
//       console.log(`Limit: ${limit}`);
//       pagination = {
//         currentPage: doc.length > 0 ? currentPage : 0, // Only assign a valid page if there are results
//         pages: Math.ceil(doc.length / limit), // Calculate total pages using the total count and limit
//       };
//     }
//     console.log(pagination);

//     if (cart && cart.items) {
//       // Create a set of offer IDs in the cart for faster lookup
//       const cartOfferIds = new Set(
//         cart.items.map((item) => item.offer._id.toString())
//       );
//       console.log(cartOfferIds);

//       // Map through the offers and add the `inCart` property dynamically
//       const offersWithInCart = doc.map((offer) => {
//         const offerData = offer._doc ? { ...offer._doc } : { ...offer }; // Use _doc if available
//         return {
//           ...offerData,
//           inCart: cartOfferIds.has(offer._id.toString()), // Add `inCart` property
//         };
//       });
//       // Send the modified offers in the response
//       return res.status(200).json({
//         status: "success",
//         results: offersWithInCart.length,
//         pagination,
//         data: offersWithInCart,
//       });
//     }

//     // SEND RESPONSE
//     res.status(200).json({
//       status: "success",
//       results: doc.length,
//       pagination,
//       data: doc,
//     });
//   });
export const getAll = (query, Model, populateOptions, key) =>
  catchAsync(async (req, res) => {
    const userId = req?.query?.userId;

    let cart, wishlist;
    let cartOfferIds = new Set();
    let wishlistOfferIds = new Set();

    if (query === "offer" && userId) {
      cart = await Cart.findOne({ user: userId }).populate("items.offer");
      wishlist = await Wishlist.find({ user: userId }).populate("offer");

      // Build Set of cart offer IDs
      if (cart?.items) {
        cartOfferIds = new Set(
          cart.items.map((item) => item.offer?._id?.toString()),
        );
      }

      // Build Set of wishlist offer IDs
      if (wishlist?.length > 0) {
        wishlistOfferIds = new Set(
          wishlist.map((item) => item.offer?._id?.toString()),
        );
      }
    }

    let features;
    let countFeatures;
    if (req.query.genres || req.query.listingType) {
      features = new APIFeatures(Model.find(), req.query, Model)
        .search(key)
        .populate(populateOptions)
        .advancefilter()
        .sort()
        .paginate();

      countFeatures = new APIFeatures(Model.find(), req.query, Model)
        .search(key)
        .advancefilter();
    } else {
      features = new APIFeatures(Model.find(), req.query, Model)
        .search(key)
        .populate(populateOptions)
        .filter()
        .sort()
        .paginate();

      countFeatures = new APIFeatures(Model.find(), req.query, Model)
        .search(key)
        .filter();
    }

    const [doc, countResult] = await Promise.all([
      features.query,
      countFeatures.query,
    ]);

    const totalDocs = countResult.length;
    // Pagination logic
    let pagination = {};
    if (doc.length > 0) {
      const currentPage = parseInt(req.query.page, 10) || 1;
      const limit = parseInt(req.query.limit, 10) || 10;
      pagination = {
        currentPage,
        pages: Math.ceil(totalDocs / limit),
        total: totalDocs,
        hasNextPage: currentPage < Math.ceil(totalDocs / limit),
        hasPrevPage: currentPage > 1,
      };
    }

    // If it's an offer query and we have cart or wishlist data
    if (query === "offer" && userId && (cart || wishlist)) {
      const offersWithFlags = doc.map((offer) => {
        const offerData = offer._doc ? { ...offer._doc } : { ...offer };
        const offerId = offer._id.toString();

        return {
          ...offerData,
          inCart: cartOfferIds.has(offerId),
          isWishlist: wishlistOfferIds.has(offerId),
        };
      });

      return res.status(200).json({
        status: "success",
        results: offersWithFlags.length,
        pagination,
        data: offersWithFlags,
      });
    }

    // Fallback response if not an offer query or no cart/wishlist
    res.status(200).json({
      status: "success",
      results: doc.length,
      pagination,
      data: doc,
    });
  });
