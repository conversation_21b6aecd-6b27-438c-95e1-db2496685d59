import Offer from "../models/offerModel.js";
import * as factory from "./handlerFactory.js";
import { subscriptionMail } from "../utils/emailTemplates/subscriptionMail.js";
import { newOfferMail } from "../utils/emailTemplates/templates.js";
import Cart from "../models/cartModel.js";
import Wishlist from "../models/wishListModel.js";
import mongoose from 'mongoose';
import Watchlist from '../models/watchlistModel.js';

// export const getOffer = factory.getOne(Offer, "template seller");
export const getOffer = factory.getOneOffer(Offer, [
  { path: "template", populate: { path: "category" } },
  { path: "seller" },
]);
export const createOffer = factory.createOne(Offer, offer=> subscriptionMail(newOfferMail(offer)));
export const updateOffer = factory.updateOne(Offer);
export const deleteOffer = factory.deleteOne(Offer);

export const getAllOffer = async (req, res, next) => {
  
  try {
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const skip = (page - 1) * limit;
    const search = req.query.search;
    let sort = {};

    if (req.query.sort) {
      const sortField = req.query.sort;
      if (sortField.startsWith("-")) {
        sort[sortField.substring(1)] = -1;
      } else {
        sort[sortField] = 1;
      }
    } else {
      sort = { createdAt: -1 };
    }
    const pipeline = [
      { $sort: sort },
      { 
        $lookup: {
          from: "templates",
          localField: "template",
          foreignField: "_id",
          as: "template"
        }
      },
      { $unwind: { path: "$template", preserveNullAndEmptyArrays: true } },
      { $lookup: {
          from: "categories",
          localField: "template.category",
          foreignField: "_id",
          as: "template.category"
        }
      },
      { $unwind: { path: "$template.category", preserveNullAndEmptyArrays: true } },
      { $lookup: {
          from: "users",
          localField: "seller",
          foreignField: "_id",
          as: "seller"
        }
      },
      { $unwind: { path: "$seller", preserveNullAndEmptyArrays: true } },
      {
        $lookup: {
          from: 'orders',
          let: { offerId: '$_id' },
          pipeline: [
            { $unwind: "$items" },
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$items.offer", { $toObjectId: "$$offerId" }] },
                    { $gte: ["$createdAt", new Date(Date.now() - 7 * 24 * 60 * 60 * 1000)] },
                    { $lte: ["$createdAt", new Date()] }
                  ]
                }
              }
            },
            { $count: "count" },
          ],
          as: 'lastWeekSales'
        }
      },
      {
        $lookup: {
          from: 'offers',
          let: { templateId: '$template._id', offerId: '$_id', offerPrice: '$customerPays' },
          pipeline: [
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$template", { $toObjectId: "$$templateId" }] },
                    { $ne: ["$_id", { $toObjectId: "$$offerId" }] },
                    { $lt: ['$customerPays', "$$offerPrice"]}
                  ]
                }
              }
            },
            { $sort: { customerPays: 1 } },
            { $limit: 1 }
          ],
          as: 'lowestPrice'
        }
      },
      {
        $lookup: {
          from: 'orders',
          let: { offerId: '$_id' },
          pipeline: [
            { $unwind: "$items" },
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$items.offer", { $toObjectId: "$$offerId" }] },
                    { $gte: ["$createdAt", new Date(Date.now() - 30 * 24 * 60 * 60 * 1000)] },
                    { $lte: ["$createdAt", new Date()] }
                  ]
                }
              }
            },
            { $count: "count" },
          ],
          as: 'lastMonthSales'
        }
      },
      {
        $lookup: {
          from: 'orders',
          let: { offerId: '$_id' },
          pipeline: [
            { $unwind: "$items" },
            {
              $match: {
                $expr: {
                  $and: [
                    { $eq: ["$items.offer", { $toObjectId: "$$offerId" }] },
                    { $gte: ["$createdAt", new Date(Date.now() - 24 * 60 * 60 * 1000)] },
                    { $lte: ["$createdAt", new Date()] }
                  ]
                }
              },
            },
            { $count: "count" },
          ],
          as: 'last24HoursSales'
        }
      },
      {
        $addFields: {
          last24HoursSales: { $ifNull: [{ $arrayElemAt: ["$last24HoursSales.count", 0] }, 0] },
          lastMonthSales: { $ifNull: [{ $arrayElemAt: ["$lastMonthSales.count", 0] }, 0] },
          lastWeekSales: { $ifNull: [{ $arrayElemAt: ["$lastWeekSales.count", 0] }, 0] },
          isHot: { $gt: [{ $ifNull: [{ $arrayElemAt: ["$lastMonthSales.count", 0] }, 0] }, 20] },
          lowestPrice: {
            $ifNull: [{ $arrayElemAt: ["$lowestPrice.customerPays", 0] }, null]
          }
        }
      }
    ];

    if (search) {
      pipeline.push(
          { $match: { name: { $regex: search, $options: "i" } } }
      );
    }

    if (req.query.seller) {
      pipeline.push({ $match: { "seller._id": mongoose.Types.ObjectId(req.query.seller) } });
    }

    if (req.query.active) {
      pipeline.push({ $match: { active: JSON.parse(req.query.active) } });
    }

    // Category filter - supports both offer category and template category
    if (req.query.category) {
      pipeline.push({
        $match: {
          $or: [
            { "category": req.query.category },
            { "template.category.name": req.query.category },
            { "template.category.slug": req.query.category }
          ]
        }
      });
    }


    // Region filter
    if (req.query.region) {
      pipeline.push({ $match: { region: { $regex: req.query.region, $options: "i" } } });
    }

    // Delivery Time filter - supports multiple values (stored as strings: 'instant', '24', '48')
    if (req.query.deliveryTime) {
      const deliveryTimes = Array.isArray(req.query.deliveryTime)
        ? req.query.deliveryTime
        : [req.query.deliveryTime];

      // Convert query parameters to strings and handle both string and numeric inputs
      const validDeliveryTimes = deliveryTimes
        .map(time => String(time).toLowerCase())
        .filter(time => time && time.trim() !== '');

      if (validDeliveryTimes.length > 0) {
        pipeline.push({
          $match: {
            deliveryTime: { $in: validDeliveryTimes }
          }
        });
      }
    }

    // Status filter (boolean)
    if (req.query.status) {
      pipeline.push({ $match: { status: JSON.parse(req.query.status) } });
    }

    // CreatedAt filter (exact date)
    if (req.query.createdAt) {
      const date = new Date(req.query.createdAt);
      const year = date.getFullYear();
      const month = date.getMonth() + 1;
      const day = date.getDate();
      pipeline.push({
        $match: {
          $expr: {
            $and: [
              { $eq: [{ $year: "$createdAt" }, year] },
              { $eq: [{ $month: "$createdAt" }, month] },
              { $eq: [{ $dayOfMonth: "$createdAt" }, day] }
            ]
          }
        }
      });
    }

    // OrderId filter (orderNumber in model)
    if (req.query.orderId) {
      pipeline.push({ $match: { orderNumber: req.query.orderId } });
    }

    // Payment filter (if present in model)
    if (req.query.payment) {
      pipeline.push({ $match: { payment: req.query.payment } });
    }

    // Dynamic selectedBoxes filter (expects JSON stringified object)
    if (req.query.selectedBoxes) {
      try {
        const selectedBoxes = JSON.parse(req.query.selectedBoxes);
        Object.entries(selectedBoxes).forEach(([key, values]) => {
          if (Array.isArray(values) && values.length > 0) {
            pipeline.push({ $match: { [key]: { $in: values } } });
          }
        });
      } catch (e) {
        // Ignore if not valid JSON
      }
    }

    // Genre filter (template.genres, case-insensitive)
    if (req.query.genre) {
      pipeline.push({ $match: { "template.genres": { $regex: req.query.genre, $options: "i" } } });
    }

    // Language filter (template.languages, case-insensitive)
    if (req.query.language) {
      const langs = req.query.language.split(',').map(lang => new RegExp(`^${lang}$`, 'i'));
      pipeline.push({ $match: { "template.languages": { $in: langs } } });
    }

    // Stock filters (stock)
    if (req.query.stock) {
      const operator = typeof (req.query.stock) === 'object' ? Object.keys(req.query.stock)[0] : 'eq';
      const value = parseFloat(typeof (req.query.stock) === 'object' ? req.query.stock[operator] : req.query.stock);
      pipeline.push({ $match: { stock: { [`$${operator}`]: value } } });
    }

    // Price Filter
    if (req.query.price) {
      const operator = typeof (req.query.price) === 'object' ? Object.keys(req.query.price)[0] : 'eq';
      const value = parseFloat(typeof (req.query.price) === 'object' ? req.query.price[operator] : req.query.price);
      pipeline.push({ $match: { customerPays: { [`$${operator}`]: value } } });
    }
    
    // Watchlist filter
    if (req.query.watchlist === 'true' && req.user && req.user._id) {
      const now = new Date();
      const watchlistEntries = await Watchlist.find({ user: req.user._id, expiresAt: { $gt: now } }).select('offer');
      const offerIds = watchlistEntries.map(entry => entry.offer);
      if (offerIds.length === 0) {
        // No offers in watchlist, return empty result
        return res.status(200).json({
          status: "success",
          results: 0,
          total: 0,
          pagination: {
            currentPage: page,
            pages: 0,
          },
          data: [],
        });
      }
      pipeline.push({ $match: { _id: { $in: offerIds } } });
    }

    const offers = await Offer.aggregate([
      ...pipeline,
      { $skip: skip },
      { $limit: limit },
    ]);
    const countResult = await Offer.aggregate([
      ...pipeline,
      {
        $count: 'total'
      }
    ]);

    const total = countResult[0]?.total || 0;
    const userId = req.query.userId;
    let cartOfferIds = new Set();
    let wishlistOfferIds = new Set();
    if (userId) {
      const cart = await Cart.findOne({ user: userId }).populate("items.offer");
      const wishlist = await Wishlist.find({ user: userId }).populate("offer");
      if (cart?.items) {
        cartOfferIds = new Set(cart.items.map(item => item.offer?._id?.toString()));
      }
      if (wishlist?.length > 0) {
        wishlistOfferIds = new Set(wishlist.map(item => item.offer?._id?.toString()));
      }
    }
    const offersWithFlags = offers.map(offer => {
      const offerId = offer._id.toString();
      return {
        ...offer,
        inCart: cartOfferIds.has(offerId),
        isWishlist: wishlistOfferIds.has(offerId),
      };
    });
    res.status(200).json({
      status: "success",
      results: offersWithFlags.length,
      total,
      pagination: {
        currentPage: page,
        pages: Math.ceil(total / limit),
      },
      data: offersWithFlags,
    });
  } catch (err) {
    next(err);
  }
};
