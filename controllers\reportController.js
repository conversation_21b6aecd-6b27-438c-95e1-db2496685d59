import asyncHandler from "express-async-handler";
import Report from "../models/reportModel.js";
import Order from "../models/OrderModel.js";
import AppError from "../utils/appError.js";
import * as factory from "./handlerFactory.js";

// Get all reports for a user
export const getUserReports = asyncHandler(async (req, res) => {
  const reports = await Report.find({ userId: req.user._id })
    .populate('orderId')
    .populate('offerId')
    .sort('-createdAt');

  res.status(200).json({
    status: "success",
    data: reports
  });
});

// Create a new report
export const createReport = asyncHandler(async (req, res, next) => {
  // Check if order exists and belongs to user
  const order = await Order.findById(req.body.orderId);
  if (!order) {
    return next(new AppError("Order not found", 404));
  }
  if (order.user.toString() !== req.user._id.toString()) {
    return next(new AppError("You can only report your own orders", 403));
  }

  // Create the report
  const report = await Report.create({
    ...req.body,
    userId: req.user._id,
    status: "Pending"
  });

  // Populate references
  await report.populate(['orderId', 'offerId']);

  res.status(201).json({
    status: "success",
    data: report
  });
});

// Get a single report
export const getReport = asyncHandler(async (req, res, next) => {
  const report = await Report.findById(req.params.id)
    .populate('orderId')
    .populate('offerId');

  if (!report) {
    return next(new AppError("Report not found", 404));
  }

  // Check if the report belongs to the user
  if (report.userId.toString() !== req.user._id.toString()) {
    return next(new AppError("You can only view your own reports", 403));
  }

  res.status(200).json({
    status: "success",
    data: report
  });
});

// Update a report (only allowed for pending reports)
export const updateReport = asyncHandler(async (req, res, next) => {
  const report = await Report.findById(req.params.id);
  
  if (!report) {
    return next(new AppError("Report not found", 404));
  }

  // Check if the report belongs to the user
  if (report.userId.toString() !== req.user._id.toString()) {
    return next(new AppError("You can only update your own reports", 403));
  }

  // Only allow updates if the report is pending
  if (report.status !== "Pending") {
    return next(new AppError("You can only update pending reports", 400));
  }

  // Don't allow updating certain fields
  const allowedUpdates = ['problemType', 'preferredSolution', 'comment', 'attachments', 'status', 'resolvedBy'];
  const updates = Object.keys(req.body)
    .filter(key => allowedUpdates.includes(key))
    .reduce((obj, key) => {
      obj[key] = req.body[key];
      return obj;
    }, {});

  // If status is being changed from Pending, add resolvedAt and resolvedBy
  if (updates.status && updates.status !== 'Pending') {
    updates.resolvedAt = new Date();
    updates.resolvedBy = req.body.resolvedBy;
  }

  const updatedReport = await Report.findByIdAndUpdate(
    req.params.id,
    updates,
    { new: true, runValidators: true }
  ).populate(['orderId', 'offerId']);

  res.status(200).json({
    status: "success",
    data: updatedReport
  });
});

// Delete a report (only allowed for pending reports)
export const deleteReport = asyncHandler(async (req, res, next) => {
  const report = await Report.findById(req.params.id);
  
  if (!report) {
    return next(new AppError("Report not found", 404));
  }

  // Check if the report belongs to the user
  if (report.userId.toString() !== req.user._id.toString()) {
    return next(new AppError("You can only delete your own reports", 403));
  }

  // Only allow deletion if the report is pending
  if (report.status !== "Pending") {
    return next(new AppError("You can only delete pending reports", 400));
  }

  await report.remove();

  res.status(204).json({
    status: "success",
    data: null
  });
}); 