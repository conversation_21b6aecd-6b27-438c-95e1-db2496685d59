import ShopOpeningRequest from "../models/shopOpeningRequestModel.js";
import * as factory from "./handlerFactory.js";
import asyncHandler from "express-async-handler";
import Shop from "../models/shopModel.js";
import User from "../models/userModel.js";

// Create a new shop opening request
export const createShopOpeningRequest = factory.createOne(ShopOpeningRequest);

// Get all shop opening requests
export const getAllShopOpeningRequests = factory.getAll(
  "",
  ShopOpeningRequest,
  [{ path: "seller" }]
);

// Get a shop opening request by ID
export const getShopOpeningRequestById = factory.getOne(
  ShopOpeningRequest,
  "seller"
);

// Update a shop opening request by ID
// export const updateShopOpeningRequest = factory.updateOne(ShopOpeningRequest);
// export const updateShopOpeningRequest = asyncHandler(async (req, res, next) => {
//   const { isApproved } = req.body;

//   // Find the request by ID
//   const shopRequest = await ShopOpeningRequest.findById(req.params.id);
//   if (!shopRequest) {
//     return res.status(404).json({ message: "Shop opening request not found" });
//   }

//   // Update approval status
//   shopRequest.isApproved = isApproved;
//   await shopRequest.save();

//   // If approved, create the shop
//   if (isApproved === "approved") {
//     const existingShop = await Shop.findOne({ seller: shopRequest.seller });
//     if (!existingShop) {
//       await Shop.create({
//         seller: shopRequest.seller,
//         shopName: shopRequest.shopName,
//         shopDescription: shopRequest.shopDescription,
//         addresses: [{ city: shopRequest.location }], // Basic address from request
//         tier: shopRequest.tier,
//       });
//     }
//   }

//   res.status(200).json({
//     message: "Shop opening request updated successfully",
//     shopRequest,
//   });
// });
export const updateShopOpeningRequest = asyncHandler(async (req, res, next) => {
  const { isApproved } = req.body;

  // Find the shop opening request by ID
  const shopRequest = await ShopOpeningRequest.findById(req.params.id);
  if (!shopRequest) {
    return res.status(404).json({ message: "Shop opening request not found" });
  }

  // Update approval status
  shopRequest.isApproved = isApproved;
  await shopRequest.save();

  // If approved, create the shop and update the user's role
  if (isApproved === "approved") {
    const existingShop = await Shop.findOne({ seller: shopRequest.seller });

    if (!existingShop) {
      await Shop.create({
        seller: shopRequest.seller,
        shopName: shopRequest.shopName,
        shopDescription: shopRequest.shopDescription,
        addresses: [{ city: shopRequest.location }], // Basic address from request
        tier: shopRequest.tier,
      });
    }

    // Find the user based on the seller ID and update their role to "seller"
    const user = await User.findById(shopRequest.seller);
    if (user && user.role !== "seller") {
      user.role = "seller";
      await user.save();
    }
  }

  res.status(200).json({
    message: "Shop opening request updated successfully",
    shopRequest,
  });
});

export const getMyShop = asyncHandler(async (req, res, next) => {
  const userId = req.user._id;
  // Find the shop by seller ID
  const shop = await ShopOpeningRequest.findOne({ seller: userId });

  if (!shop) {
    return res.status(404).json({
      status: 404,
      message: "Shop not found",
    });
  }

  res.status(200).json({
    status: 200,
    shopStatus: shop.isApproved,
  });
});

// Delete a shop opening request by ID
export const deleteShopOpeningRequest = factory.deleteOne(ShopOpeningRequest);
