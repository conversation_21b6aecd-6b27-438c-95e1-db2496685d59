import SubCategory from "./../models/subcategoryModel.js"; // Assuming you have a SubCategory model
import fs from "fs";
import csv from "csv-parser";
import slugify from "slugify";
import mongoose from "mongoose";

// Create a new subcategory
export const createSubCategory = async (req, res) => {
  const {
    description,
    categoryName,
    metaTitle,
    metaDescription,
    metaKeywords,
    category_order,
    visibility,
    showInMainMenu,
    showImageOnMainMenu,
    image,
    slug,
    parent_id, // Parent subcategory reference
  } = req.body;

  try {
    // Create a new subcategory instance
    const subCategory = new SubCategory({
      description,
      categoryName,
      metaTitle,
      metaDescription,
      metaKeywords,
      category_order,
      visibility,
      showInMainMenu,
      showImageOnMainMenu,
      image,
      slug: slug || slugify(categoryName.en, { lower: true }), // Generate slug if not provided
      parent_id: parent_id || null, // Ensure it's null if not provided
    });

    // Save the subcategory to the database
    await subCategory.save();

    res.status(201).json({
      status: "success",
      message: "Subcategory created successfully",
      data: subCategory,
    });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ status: "fail", message: "Server error", error: error.message });
  }
};

// Get a subcategory by ID
export const getSubCategoryById = async (req, res) => {
  try {
    const subCategory = await SubCategory.findOne({ _id: req.params.id });

    const childSubCategories = await SubCategory.find({
      parent_id: subCategory._id,
    });

    if (!subCategory) {
      return res
        .status(404)
        .json({ status: "fail", message: "Subcategory not found" });
    }

    res.status(200).json({
      status: "success",
      data: { ...subCategory.toObject(), children: childSubCategories },
    });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ status: "fail", message: "Server error", error: error.message });
  }
};

export const getAllSubCategories = async (req, res) => {
  try {
    const { sortBy, order = "asc", visibility, parentOnly } = req.query;

    // Map query parameters to sorting logic
    const sortOptions = {
      categoryOrder: { category_order: order === "asc" ? 1 : -1 },
      date: { createdAt: order === "asc" ? 1 : -1 },
      alphabetical: { name: order === "asc" ? 1 : -1 },
    };

    // Determine the sort field, defaulting to `categoryOrder`
    const sortField = sortOptions[sortBy] || sortOptions.categoryOrder;

    // Build the query filters
    const filters = {};
    if (visibility !== undefined) {
      filters.visibility = visibility === "true"; // Convert string to boolean
    }
    filters.isActive = true; // Only include active subcategories
    if (parentOnly === "true") {
      filters.parent_id = null; // Only include parent subcategories
    }

    // Fetch parent subcategories with filters and sorting applied
    const parentSubCategories = await SubCategory.find({
      ...filters,
      parent_id: null,
    }).sort(sortField);

    // Populate children for each parent subcategory
    const populatedSubCategories = await Promise.all(
      parentSubCategories.map(async (parent) => {
        const children = await SubCategory.find({
          parent_id: parent._id,
          isActive: true,
        });
        return { ...parent.toObject(), children };
      })
    );

    res.status(200).json({ status: "success", data: populatedSubCategories });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ status: "fail", message: "Server error", error: error.message });
  }
};
// Update a subcategory by ID
export const updateSubCategory = async (req, res) => {
  const {
    name,
    description,
    categoryName,
    metaTitle,
    metaDescription,
    metaKeywords,
    category_order,
    visibility,
    showInMainMenu,
    showImageOnMainMenu,
    image,
  } = req.body;

  try {
    const subCategory = await SubCategory.findById(req.params.id);

    if (!subCategory) {
      return res
        .status(404)
        .json({ status: "fail", message: "Subcategory not found" });
    }

    // Update the subcategory fields
    subCategory.name = name || subCategory.name;
    subCategory.description = description || subCategory.description;
    subCategory.categoryName = categoryName || subCategory.categoryName;
    subCategory.metaTitle = metaTitle || subCategory.metaTitle;
    subCategory.metaDescription =
      metaDescription || subCategory.metaDescription;
    subCategory.metaKeywords = metaKeywords || subCategory.metaKeywords;
    subCategory.category_order = category_order || subCategory.category_order;
    subCategory.visibility =
      visibility !== undefined ? visibility : subCategory.visibility;
    subCategory.showInMainMenu =
      showInMainMenu !== undefined
        ? showInMainMenu
        : subCategory.showInMainMenu;
    subCategory.showImageOnMainMenu =
      showImageOnMainMenu !== undefined
        ? showImageOnMainMenu
        : subCategory.showImageOnMainMenu;
    subCategory.image = image || subCategory.image;
    // subCategory.category = category || subCategory.category;

    // Save the updated subcategory
    await subCategory.save();

    res.status(200).json({
      status: "success",
      message: "Subcategory updated successfully",
      data: subCategory,
    });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ status: "fail", message: "Server error", error: error.message });
  }
};
// Delete a subcategory by ID
export const deleteSubCategory = async (req, res) => {
  try {
    const subCategory = await SubCategory.findById(req.params.id);

    if (!subCategory) {
      return res
        .status(404)
        .json({ status: "fail", message: "Subcategory not found" });
    }
    // Delete the subcategory
    subCategory.isActive = false;
    await subCategory.save();
    res
      .status(200)
      .json({ status: "success", message: "Subcategory deleted successfully" });
  } catch (error) {
    console.error(error);
    res
      .status(500)
      .json({ status: "fail", message: "Server error", error: error.message });
  }
};

export const bulkUploadCategories = async (req, res) => {
  const csvFilePath = req.file.path; // Path to the uploaded CSV file
  //    res.status(200).json({ message: 'Categories uploaded successfully' });
  try {
    const categories = [];

    // Read and parse the CSV file
    await new Promise((resolve, reject) => {
      fs.createReadStream(csvFilePath)
        .pipe(csv())
        .on("data", (row) => {
          const {
            name,
            slug,
            parent_id,
            description,
            metaKeywords,
            category_order,
          } = row;

          categories.push({
            name,
            description,
            categoryName: {
              en: name, // Assuming `name` is in English; adjust if needed
            },
            slug: slug || slugify(name, { lower: true }), // Generate slug if not provided
            metaTitle: name, // Set meta title as the name, customize as needed
            metaDescription: description,
            metaKeywords: metaKeywords, // Convert comma-separated keywords to array
            category_order: parseInt(category_order, 10),
            parent_id:
              parent_id && mongoose.isValidObjectId(parent_id)
                ? parent_id
                : null,
          });
        })
        .on("end", resolve)
        .on("error", reject);
    });

    // Insert the parsed data into the database
    const subcatagories = await SubCategory.insertMany(categories);
    // Delete the CSV file after processing
    fs.unlinkSync(csvFilePath);
    res.status(200).json({
      status: "success",
      subcatagories,
      message: "Categories uploaded successfully",
    });
  } catch (error) {
    // Delete the CSV file in case of error
    if (fs.existsSync(csvFilePath)) {
      fs.unlinkSync(csvFilePath);
    }
    console.error("Error uploading categories:", error);
    res.status(500).json({ message: "Error uploading categories", error });
  }
};
