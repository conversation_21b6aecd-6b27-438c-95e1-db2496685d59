import Ticket from "./../models/ticketModel.js";
import * as factory from "./handlerFactory.js";
import {addConversation} from "../utils/ticketUtils.js";

// Create a new ticket
export const createTicket = async (req, res, next) => {
  try {
    const ticketData = req.body;
    if (req.user && req.user._id) {
      ticketData.user = req.user._id;
    }
    const ticket = await Ticket.create(ticketData);
    res.status(201).json({ status: 'success', data: ticket });
  } catch (err) {
    next(err);
  }
};

// Get all tickets (admin only or ticket owner)
export const getAllTickets = async (req, res, next) => {
  try {
    let filter = {};
    if (!req.user || req.user.role !== 'admin') {
      filter.user = req.user._id;
    }
    const page = parseInt(req.query.page, 10) || 1;
    const limit = parseInt(req.query.limit, 10) || 10;
    const skip = (page - 1) * limit;
    const total = await Ticket.countDocuments(filter);
    const tickets = await Ticket.find(filter)
      .populate('user', 'name email')
      .sort({ createdAt: -1 })
      .skip(skip)
      .limit(limit);
    const pages = Math.ceil(total / limit);
    res.status(200).json({
      status: 'success',
      results: tickets.length,
      pagination: {
        currentPage: page,
        pages,
        total,
        hasNextPage: page < pages,
        hasPrevPage: page > 1
      },
      data: tickets,
    });
  } catch (err) {
    next(err);
  }
};

// Get a ticket by ID with enhanced details
export const getTicketById = async (req, res, next) => {
  try {
    const ticketId = req.params.id;
    const userId = req.user._id;
    const userRole = req.user.role;

    // Build query with population
    let query = Ticket.findById(ticketId)
      .populate('user', 'firstName lastName email avatar role')
      .populate({
        path: 'conversationId',
        populate: [
          {
            path: 'client',
            select: 'firstName lastName email avatar role'
          },
          {
            path: 'seller',
            select: 'firstName lastName email avatar role'
          }
        ]
      });

    const ticket = await query;

    if (!ticket) {
      return res.status(404).json({
        status: 'fail',
        message: 'No ticket found with that ID'
      });
    }

    // Check authorization - user can only see their own tickets unless admin
    if (userRole !== 'admin' && ticket.user && ticket.user._id.toString() !== userId.toString()) {
      return res.status(403).json({
        status: 'fail',
        message: 'You can only access your own tickets'
      });
    }

    // For guest tickets, check if email matches (if provided in query)
    if (!ticket.user && userRole !== 'admin') {
      const emailQuery = req.query.email;
      if (!emailQuery || ticket.email !== emailQuery.toLowerCase()) {
        return res.status(403).json({
          status: 'fail',
          message: 'Access denied'
        });
      }
    }

    res.status(200).json({
      status: 'success',
      data: ticket
    });

  } catch (err) {
    next(err);
  }
};

// Get user's tickets with enhanced filtering and pagination
export const getUserTickets = async (req, res, next) => {
  try {
    const userId = req.user._id;
    const {
      page = 1,
      limit = 10,
      status,
      inquiryType,
      search,
      sortBy = 'createdAt',
      sortOrder = 'desc'
    } = req.query;

    // Parse pagination
    const parsedPage = Math.max(1, parseInt(page));
    const parsedLimit = Math.max(1, Math.min(50, parseInt(limit))); // Max 50 items per page
    const skip = (parsedPage - 1) * parsedLimit;

    // Build filter
    const filter = { user: userId };

    if (status) {
      filter.status = status;
    }

    if (inquiryType) {
      filter.inquiryType = inquiryType;
    }

    // Build search query
    if (search) {
      filter.$or = [
        { subject: { $regex: search, $options: 'i' } },
        { description: { $regex: search, $options: 'i' } },
        { ticketNumber: { $regex: search, $options: 'i' } }
      ];
    }

    // Build sort object
    const sort = {};
    sort[sortBy] = sortOrder === 'desc' ? -1 : 1;

    // Get total count for pagination
    const total = await Ticket.countDocuments(filter);

    // Get tickets with population
    const tickets = await Ticket.find(filter)
      .populate('user', 'firstName lastName email avatar')
      .populate('conversationId', 'lastMessage unreadClient unreadSeller')
      .sort(sort)
      .skip(skip)
      .limit(parsedLimit);

    // Calculate pagination info
    const totalPages = Math.ceil(total / parsedLimit);

    res.status(200).json({
      status: 'success',
      results: tickets.length,
      pagination: {
        currentPage: parsedPage,
        totalPages,
        total,
        hasNextPage: parsedPage < totalPages,
        hasPrevPage: parsedPage > 1,
        limit: parsedLimit
      },
      data: tickets
    });

  } catch (err) {
    next(err);
  }
};

// Update a ticket's status
export const updateTicketStatus = factory.updateOne(Ticket);

// Delete a ticket
export const deleteTicket = factory.deleteOne(Ticket);
