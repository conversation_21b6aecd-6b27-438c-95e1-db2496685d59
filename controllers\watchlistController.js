import Watchlist from '../models/watchlistModel.js';
import Offer from '../models/offerModel.js';
import Cart from '../models/cartModel.js';
import asyncHandler from 'express-async-handler';

// @desc    Add offer to watchlist
// @route   POST /api/watchlist
// @access  Private
export const addToWatchlist = asyncHandler(async (req, res) => {
  const { offerId } = req.body;
  const userId = req.user._id;
  const offer = await Offer.findById(offerId);
  if (!offer) {
    res.status(404);
    throw new Error('Offer not found');
  }
  const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
  let watchlistItem = await Watchlist.findOne({ user: userId, offer: offerId });
  if (watchlistItem) {
    watchlistItem.expiresAt = expiresAt;
    await watchlistItem.save();
  } else {
    watchlistItem = await Watchlist.create({ user: userId, offer: offerId, expiresAt });
  }
  res.status(201).json(watchlistItem);
});

// @desc    Remove offer from watchlist
// @route   DELETE /api/watchlist/:id
// @access  Private
export const removeFromWatchlist = asyncHandler(async (req, res) => {
  const watchlistItem = await Watchlist.findOne({
    _id: req.params.id,
    user: req.user._id,
  });
  if (!watchlistItem) {
    res.status(404);
    throw new Error('Watchlist item not found');
  }
  await watchlistItem.deleteOne();
  res.status(200).json({ message: 'Offer removed from watchlist' });
});

// @desc    Get user's watchlist (paginated)
// @route   GET /api/watchlist
// @access  Private
export const getWatchlist = asyncHandler(async (req, res) => {
  const userId = req.user._id;
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const skip = (page - 1) * limit;
  const now = new Date();
  let cart = await Cart.findOne({ user: userId }).populate('items.offer');
  const cartOfferIds = new Set(cart?.items?.map(item => item.offer?._id?.toString()) || []);
  const totalWatchlist = await Watchlist.countDocuments({ user: userId, expiresAt: { $gt: now } });
  const watchlist = await Watchlist.find({ user: userId, expiresAt: { $gt: now } })
    .populate({
      path: 'offer',
      populate: [
        { path: 'template', model: 'Template' },
        { path: 'seller', model: 'User' },
      ],
    })
    .sort('-createdAt')
    .skip(skip)
    .limit(limit);
  const watchlistWithInCart = watchlist.map(entry => {
    const offer = entry.offer;
    const offerData = offer?._doc ? { ...offer._doc } : { ...offer };
    return {
      ...entry._doc,
      offer: {
        ...offerData,
        inCart: cartOfferIds.has(offer._id.toString()),
      },
    };
  });
  const pagination = {
    currentPage: watchlistWithInCart.length > 0 ? page : 0,
    pages: Math.ceil(totalWatchlist / limit),
    total: totalWatchlist,
  };
  res.status(200).json({
    status: 'success',
    results: watchlistWithInCart.length,
    pagination,
    data: watchlistWithInCart,
  });
});

// @desc    Toggle offer in watchlist
// @route   POST /api/watchlist/toggle
// @access  Private
export const toggleWatchlist = asyncHandler(async (req, res) => {
  const { offerId } = req.body;
  const userId = req.user._id;
  const offer = await Offer.findById(offerId);
  if (!offer) {
    res.status(404);
    throw new Error('Offer not found');
  }
  const existingWatchlistItem = await Watchlist.findOne({ user: userId, offer: offerId });
  if (existingWatchlistItem) {
    await existingWatchlistItem.deleteOne();
    return res.status(200).json({ message: 'Offer removed from watchlist' });
  }
  const expiresAt = new Date(Date.now() + 30 * 24 * 60 * 60 * 1000);
  const watchlistItem = await Watchlist.create({ user: userId, offer: offerId, expiresAt });
  res.status(201).json(watchlistItem);
});

// @desc    Get count of active watchlist offers for user
// @route   GET /api/watchlist/count
// @access  Private
export const getWatchlistCount = asyncHandler(async (req, res) => {
  const userId = req.user._id;
  const now = new Date();
  const count = await Watchlist.countDocuments({ user: userId, expiresAt: { $gt: now } });
  res.status(200).json({ status: 'success', count });
});

// Get all active offers in user's watchlist
export const getUserWatchlistOffers = asyncHandler(async (req, res) => {
  const userId = req.user._id;
  const now = new Date();
  const watchlistEntries = await Watchlist.find({ user: userId, expiresAt: { $gt: now } }).select('offer');
  const offerIds = watchlistEntries.map(entry => entry.offer);
  const offers = await Offer.find({ _id: { $in: offerIds } });
  res.status(200).json({ status: 'success', data: offers });
}); 