import Wishlist from "../models/wishListModel.js";
import Offer from "../models/offerModel.js";
import Cart from "../models/cartModel.js";
import asyncHandler from "express-async-handler";
import {notifyUser} from "../services/notificationService.js";

// @desc    Add offer to wishlist
// @route   POST /api/wishlist
// @access  Private
const addToWishlist = asyncHandler(async (req, res) => {
  const { offerId } = req.body;

  const userId = req.user._id;

  const offer = await Offer.findById(offerId);
  if (!offer) {
    res.status(404);
    throw new Error("Offer not found");
  }

  const existingWishlistItem = await Wishlist.findOne({
    user: req.user._id,
    offer: offerId,
  });

  if (existingWishlistItem) {
    res.status(400);
    throw new Error("Offer already in wishlist");
  }

  const wishlistItem = await Wishlist.create({
    user: req.user._id,
    offer: offerId,
  });

  notifyUser({userId, message:`${offer.name} added to wishlist`, title:"Add to wishlist!", entityId:offerId, entityType:"wishlist"}).finally()

  res.status(201).json(wishlistItem);
});

// @desc    Remove offer from wishlist
// @route   DELETE /api/wishlist/:id
// @access  Private
const removeFromWishlist = asyncHandler(async (req, res) => {
  const wishlistItem = await Wishlist.findOne({
    _id: req.params.id,
    user: req.user._id,
  });

  if (!wishlistItem) {
    res.status(404);
    throw new Error("Wishlist item not found");
  }

  await wishlistItem.deleteOne();
  res.status(200).json({ message: "Offer removed from wishlist" });
});

// @desc    Get user's wishlist
// @route   GET /api/wishlist
// @access  Private
// const getWishlist = asyncHandler(async (req, res) => {
//   const wishlist = await Wishlist.find({ user: req.user._id })
//     .populate({
//       path: "offer",
//       select:
//         "name category expectedPrice customerPays deliveryTime sold ratings",
//     })
//     .sort("-createdAt");

//   res.status(200).json(wishlist);
// });
// const getWishlist = asyncHandler(async (req, res) => {
//   const userId = req.user._id;

//   // Get cart and extract offer IDs for comparison
//   let cart = await Cart.findOne({ user: userId }).populate("items.offer");
//   const cartOfferIds = new Set(
//     cart?.items?.map((item) => item.offer?._id?.toString()) || []
//   );

//   // Get wishlist with deeply populated offer fields
//   const wishlist = await Wishlist.find({ user: userId })
//     .populate({
//       path: "offer",
//       populate: [
//         {
//           path: "template",
//           model: "Template",
//         },
//         {
//           path: "seller",
//           model: "User",
//         },
//       ],
//     })
//     .sort("-createdAt");

//   // Add `inCart` to each wishlist offer
//   const wishlistWithInCart = wishlist.map((entry) => {
//     const offer = entry.offer;
//     const offerData = offer?._doc ? { ...offer._doc } : { ...offer };
//     return {
//       ...entry._doc, // include wishlist metadata like _id, timestamps, etc.
//       offer: {
//         ...offerData,
//         inCart: cartOfferIds.has(offer._id.toString()),
//       },
//     };
//   });

//   res.status(200).json({
//     status: "success",
//     results: wishlistWithInCart.length,
//     data: wishlistWithInCart,
//   });
// });

const getWishlist = asyncHandler(async (req, res) => {
  const userId = req.user._id;

  // Pagination setup
  const page = parseInt(req.query.page, 10) || 1;
  const limit = parseInt(req.query.limit, 10) || 10;
  const skip = (page - 1) * limit;

  // Get user's cart and extract offer IDs
  let cart = await Cart.findOne({ user: userId }).populate("items.offer");
  const cartOfferIds = new Set(
    cart?.items?.map((item) => item.offer?._id?.toString()) || []
  );

  // Count total wishlist items for pagination
  const totalWishlist = await Wishlist.countDocuments({ user: userId });

  // Fetch paginated wishlist
  const wishlist = await Wishlist.find({ user: userId })
    .populate({
      path: "offer",
      populate: [
        { path: "template", model: "Template" },
        { path: "seller", model: "User" },
      ],
    })
    .sort("-createdAt")
    .skip(skip)
    .limit(limit);

  // Add inCart flag to each wishlist item
  const wishlistWithInCart = wishlist.map((entry) => {
    const offer = entry.offer;
    const offerData = offer?._doc ? { ...offer._doc } : { ...offer };
    return {
      ...entry._doc,
      offer: {
        ...offerData,
        inCart: cartOfferIds.has(offer._id.toString()),
      },
    };
  });

  // Prepare pagination object
  const pagination = {
    currentPage: wishlistWithInCart.length > 0 ? page : 0,
    pages: Math.ceil(totalWishlist / limit),
    total: totalWishlist,
  };

  // Return final response
  res.status(200).json({
    status: "success",
    results: wishlistWithInCart.length,
    pagination,
    data: wishlistWithInCart,
  });
});

const toggleWishlist = asyncHandler(async (req, res) => {
  const { offerId } = req.body;
  const userId = req.user._id;

  // Check if offer exists
  const offer = await Offer.findById(offerId);
  if (!offer) {
    res.status(404);
    throw new Error("Offer not found");
  }

  // Check if the offer is already in wishlist
  const existingWishlistItem = await Wishlist.findOne({
    user: req.user._id,
    offer: offerId,
  });

  if (existingWishlistItem) {
    // Remove from wishlist
    await existingWishlistItem.deleteOne();
    return res.status(200).json({ message: "Offer removed from wishlist" });
  }

  notifyUser({userId, message:`${offer.name} added to wishlist`, title:"Add to wishlist!", entityId:offerId, entityType:"review"}).finally()

  // Add to wishlist
  const wishlistItem = await Wishlist.create({
    user: req.user._id,
    offer: offerId,
  });

  res.status(201).json(wishlistItem);
});

export { addToWishlist, removeFromWishlist, getWishlist, toggleWishlist };
