import mongoose from "mongoose";

const bannerSchema = new mongoose.Schema(
  {
    images: {
      phone: { type: String },
      desktop: { type: String },
      tablet: { type: String },
    },
    order: {
      type: Number,
      default: 0,
    },
    category: {
      type: String,
    },
    title: {
      type: String,
    },
    logoImage: {
      type: String,
    },
      tag: {
      type: String,
    },
    shortSummary: {
      type: String,
    },
      discountPercent: {
          type: String,
      },
      couponCode: {
          type: String,
      },
      existingPrice: {
          type: String,
          required: [true, "Price is required"],
      },
    status: {
      type: Boolean,
      default: true,
    },
    link: {
      type: String,
    },
    startTime: {
      type: Date,
      required: [true, "Start time for the banner is required"],
    },
    endTime: {
      type: Date,
      required: [true, "End time for the banner is required"],
    },
  },
  { timestamps: true }
);

// Virtual field to check if the banner is currently active
bannerSchema.virtual("isActive").get(function () {
  const now = new Date();
  return this.status && now >= this.startTime && now <= this.endTime;
});

const Banner = mongoose.model("Banner", bannerSchema);

export default Banner;
