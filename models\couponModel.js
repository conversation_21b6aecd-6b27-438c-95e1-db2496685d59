import mongoose from "mongoose";

const CouponSchema = new mongoose.Schema(
  {
    code: { type: String, unique: true, required: true }, // Unique coupon code
    discountType: {
      type: String,
      enum: ["fixed", "percentage"],
      required: true,
    }, // Fixed or percentage discount
    discountValue: { type: Number, required: true }, // Discount amount or percentage
    totalCoupons: { type: Number, required: true }, // Number of coupons available
    minOrderAmount: { type: Number, default: 0 }, // Minimum order amount required
    couponUserType: {
      type: String,
      enum: ["single", "multiple"],
      default: "multiple",
    }, // User type restrictions
    usageLimitPerUser: { type: Number, default: 1 }, // Number of times a user can use the coupon
    usedBy: [{
      user: { type: mongoose.Schema.Types.ObjectId, ref: "User" },
      usageCount: { type: Number, default: 0 }
    }], // Track usage by each user
    expirationDate: { type: Date, required: true }, // Expiry date of the coupon
    applicableCategories: [
      {
        type: mongoose.Schema.Types.ObjectId,
        ref: "SubCategory", // Reference to subcategories
      },
    ],
    appliesToAllCategories: { type: Boolean, default: false }, // If true, applies to all categories
    isActive: { type: Boolean, default: true }, // Active or not
    seller: { type: mongoose.Schema.Types.ObjectId, ref: "User" }, // Reference to seller who created the coupon
    applicableClients: [{
      type: mongoose.Schema.Types.ObjectId,
      ref: "User" // Reference to specific clients this coupon applies to
    }], // List of specific clients this coupon applies to
  },
  { timestamps: true }
);

// Add method to check if a user can use the coupon
CouponSchema.methods.canUseCoupon = async function(userId) {
  // Check if coupon is active
  if (!this.isActive) return false;

  // Check if coupon has expired
  if (new Date() > this.expirationDate) return false;

  // Check if total coupons are available
  if (this.totalCoupons <= 0) return false;

  // Check if user is in applicable clients list
  const isApplicableClient = this.applicableClients.some(
    clientId => clientId.toString() === userId.toString()
  );
  if (!isApplicableClient) return false;

  // Check usage limit per user
  const userUsage = this.usedBy.find(
    usage => usage.user.toString() === userId.toString()
  );
  
  if (userUsage && userUsage.usageCount >= this.usageLimitPerUser) {
    return false;
  }

  return true;
};

const Coupon = mongoose.model("Coupon", CouponSchema);
export default Coupon;
