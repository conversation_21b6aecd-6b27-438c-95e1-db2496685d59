import mongoose from 'mongoose';

const EntrySchema = new mongoose.Schema({
    userId: { type: mongoose.Schema.Types.ObjectId, ref: 'User', required: true },
    giveawayId: { type: mongoose.Schema.Types.ObjectId, ref: 'Giveaway', required: true },
    actionType: { type: String, required: true }, // e.g., 'follow_facebook'
    points: { type: Number, default: 0 },
    createdAt: { type: Date, default: Date.now }
});

EntrySchema.index({ userId: 1, giveawayId: 1, actionType: 1 }, { unique: true }); // Prevent duplicates

export default mongoose.model('Entry', EntrySchema);
