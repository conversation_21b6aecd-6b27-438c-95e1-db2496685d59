import mongoose from "mongoose";

const faqSchema = new mongoose.Schema(
  {
    title: { type: String, required: true }, // FAQ question/title
    content: { type: String, required: true }, // FAQ answer/content (supports rich text)
    page: { type: String, required: true }, // Page where this FAQ belongs (e.g., "referral", "pricing", etc.)
    order: { type: Number, default: 0 }, // Controls the display order of FAQs
  },
  { timestamps: true } // Adds createdAt and updatedAt fields
);

export default mongoose.model("FAQ", faqSchema);
