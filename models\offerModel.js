import mongoose from "mongoose";

const ratingSchema = new mongoose.Schema({
  reviewId: {
    type: Number,
    unique: true,
  }, // Auto-incremented review ID
  offerId: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "Offer",
  }, // Reference to the offer
  user: {
    type: mongoose.Schema.Types.ObjectId,
    ref: "User",
  }, // User who submitted the rating
  userAvatar: {
    type: String,
  }, // User's avatar
  userName: {
    type: String,
  }, // User's name
  stars: {
    type: Number,

    min: 1,
    max: 5,
  }, // Number of stars (1 to 5)
  content: {
    type: String,
  }, // Review text
  helpful: {
    type: Number,
    default: 0,
  }, // Helpful counter
  notHelpful: {
    type: Number,
    default: 0,
  }, // Not helpful counter
  sellerResponse: {
    type: String,
    default: "",
  }, // <PERSON><PERSON>'s response to the review
  sellerAvatar: {
    type: String,
  }, // <PERSON><PERSON>'s avatar
  sellerName: {
    type: String,
  }, // <PERSON><PERSON>'s name
  orderNumber: {
    type: String,
  }, // Order number for the review
  createdAt: {
    type: Date,
    default: Date.now,
  }, // Timestamp for the rating
});

const offerSchema = new mongoose.Schema(
  {
    template: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Template",
      required: true,
      unique: true,
    }, // Reference to the template
    seller: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    }, // Reference to the seller
    name: { type: String }, // Name of the offer
    category: { type: String, required: true }, // Platform for the offer
    deliveryTime: { type: String }, // Delivery time for the offer
    subcategory: { type: String, default: "not assigned" }, // Platform for the offer
    region: { type: String }, // Region for the offer
    expectedPrice: { type: Number, required: true }, // Seller's price
    customerPays: { type: Number, required: true }, // Seller's price
    instantDelivery: { type: Boolean, required: true }, // Instant delivery or manual
    licenseKeys: [{ type: String }], // Array of license keys for instant delivery
    usedKeys: [{ 
      key: { type: String },
      orderId: { type: mongoose.Schema.Types.ObjectId, ref: 'Order' },
      usedAt: { type: Date, default: Date.now }
    }], // Track used keys and their associated orders
    stock: { type: Number, default: 0 }, // Calculated from licenseKeys.length
    sold: { type: Number, default: 0 }, // Number of times the offer has been sold
    active: { type: Boolean, default: true }, // Whether the offer is active or not
    extraCharge: { type: Number, default: 20 }, // Percentage extra charge
    link: { type: String }, // Optional external link
    pageViews: { type: Number, default: 0 }, // Count of how many times the offer page has been viewed
    draft: { type: Boolean, default: false }, // Whether the offer is in draft mode
    demoUrl: { type: String }, // URL for a demo version of the offer
    filesIncluded: [{ type: String }], // Array of strings describing files included
    status: { type: Boolean, default: true }, // Status of the offer
    visibility: { type: Boolean, default: true }, // Visibility of the offer
    createdAt: { type: Date, default: Date.now }, // Timestamp for when the offer was created
    featured: { type: Boolean, default: false }, // Whether the offer is featured or not
    specialOffer: { type: Boolean, default: false }, // Whether the offer is a special offer
    addedFee: { type: Number, default: 0 }, // Additional fee for the offer
    ratings: [ratingSchema],
  },
  { timestamps: true }
);
// Pre-save middleware to automatically calculate stock
offerSchema.pre("save", async function (next) {
  // Query the Template model to get the templateName and set the name of the offer
  if (this.isNew || this.isModified("template")) {
    const Template = mongoose.model("Template"); // Load the Template model
    const template = await Template.findById(this.template).select(
      "templateName"
    );
    if (template) {
      this.name = template.templateName; // Set the name of the offer to the templateName
    } else {
      return next(new Error("Template not found"));
    }
  }
  next();
});

const Offer = mongoose.model("Offer", offerSchema);
export default Offer;
