const mongoose = require('mongoose');

const CategorySchema = new mongoose.Schema({
  name: { type: String, required: true },
  subCategories: [{
    name: { type: String, required: true },
  }],
});

const SellerSchema = new mongoose.Schema({
  name: { type: String, required: true },
  startedAt: { type: Date, required: true },
  tags: { type: [String], default: [] },
  rating: { type: Number, required: true, min: 0, max: 5 },
  isTopSeller: { type: Boolean, default: false },
  totalOrders: { type: Number, default: 0 },
  feedback: { type: String },
  followers: { type: Number, default: 0 },
  reviews: [{
    text: { type: String, required: true },
    likes: { type: Number, default: 0 },
    dislikes: { type: Number, default: 0 },
    response: { type: String },
  }],
  bestSellers: [{
    type: mongoose.Schema.Types.ObjectId,
    ref: 'Product',
  }],
  selectedProducts: [{
    product: { type: mongoose.Schema.Types.ObjectId, ref: 'Product' },
    isPromoted: { type: Boolean, default: false },
  }],
});

  






module.exports = { Category, Seller, Product, Request };