import mongoose from "mongoose";

const ShopOpeningRequestSchema = new mongoose.Schema(
  {
    shopName: { type: String, required: true },
    firstName: { type: String, required: true },
    lastName: { type: String, required: true },
    phoneNumber: { type: String, required: true },
    location: { type: String, required: true },
    shopDescription: { type: String },
    tier: {
      type: String,
      enum: ["basic", "premium", "enterprise"],
      required: true,
    },
    isApproved: {
      type: String,
      enum: ["waiting", "approved", "rejected"],
      default: "waiting",
    },
    seller: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "User",
      required: true,
    },
    isActive: { type: Boolean, default: true },
    createdAt: { type: Date, default: Date.now },
  },
  { timestamps: true }
);

const ShopOpeningRequest = mongoose.model(
  "ShopOpeningRequest",
  ShopOpeningRequestSchema
);

export default ShopOpeningRequest;
