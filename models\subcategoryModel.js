import mongoose from "mongoose";

const SubCategorySchema = new mongoose.Schema(
  {
    name: { type: String },
    // Multilingual support for category name and description
    categoryName: {
      en: { type: String },
      fr: { type: String },
      it: { type: String },
      de: { type: String },
      es: { type: String },
    },
    description: { type: String },
    slug: { type: String, unique: true },
    metaTitle: { type: String }, // Simple meta title
    metaDescription: { type: String }, // Description meta tag
    metaKeywords: { type: String }, // Keywords meta tag
    // Other settings for the subcategory
    category_order: { type: Number, required: true }, // Order in the category
    visibility: { type: Boolean, default: true }, // Visible or not
    showInMainMenu: { type: Boolean, default: true }, // Show in main menu
    showImageOnMainMenu: { type: Boolean, default: true }, // Show image on the main menu
    isActive: { type: Boolean, default: true },
    image: { type: String }, // Image URL or path
    // Reference to the Category
    parent_id: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "SubCategory",
      default: null,
    },
  },
  { timestamps: true }
);

// Pre-save middleware to automatically calculate stock
SubCategorySchema.pre("save", function (next) {
  if (this.categoryName) {
    this.name = this.categoryName && this.categoryName.en;
  }
  next();
});

const SubCategory = mongoose.model("SubCategory", SubCategorySchema);
export default SubCategory;
