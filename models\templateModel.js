import mongoose from "mongoose";
import slugify from "slugify";

// Define the details for each language directly inside the schema
const templateSchema = new mongoose.Schema(
  {
    coverImage: { type: String }, // URL to the cover image
    active: { type: Boolean, default: true }, // If the template is active or not
    templateName: { type: String },
    slug: { type: String }, // Slug for SEO-friendly URL
    listingType: { type: String, enum: ['account', 'key'] }, // Listing types
    category: { type: mongoose.Schema.Types.ObjectId, ref: "SubCategory" }, // Category reference
    subcategory: { type: mongoose.Schema.Types.ObjectId, ref: "SubCategory" }, // Category reference
    region: { type: String }, // Region (e.g., 'US', 'EU')
    genres: { type: String }, // List of genres
    releaseDate: { type: Date }, // Release date
    preOrder: { type: Boolean, default: false }, // Pre-order flag
    dlc: { type: Boolean, default: false }, // Pre-order flag
    price: { type: Number }, // Price of the template
    specificCountrySellingOption: { type: Boolean, default: false }, // Country where the template will be sold
    // Price specific to this country
    // ,
    languages: [{ type: String }],
    videos: [{ type: String }], // Array of URLs for videos related to the template
    images: [{ type: String }], // Array of image URLs
    serviceFee: {
      type: String,
    },
    details: {
      title: { type: String },
      description: { type: String }, // Rich text or markdown
      seo: {
        metaTitle: { type: String },
        metaDescription: { type: String },
        metaKeywords: { type: String },
      },
      germanDetails: {
        title: { type: String },
        description: { type: String }, // Rich text for German description
        seoKeywords: { type: String }, // SEO Keywords for German
      },
      frenchDetails: {
        title: { type: String },
        description: { type: String }, // Rich text for French description
        seoKeywords: { type: String }, // SEO Keywords for French
      },
      spanishDetails: {
        title: { type: String },
        description: { type: String }, // Rich text for Spanish description
        seoKeywords: { type: String }, // SEO Keywords for Spanish
      },
      italianDetails: {
        title: { type: String },
        description: { type: String }, // Rich text for Italian description
        seoKeywords: { type: String }, // SEO Keywords for Italian
      },
    },
  },
  { timestamps: true }
);

// Pre-save middleware to create the slug
templateSchema.pre("save", function (next) {
  if (!this.slug) {
    // Use slugify to generate the slug from the template name
    this.slug = slugify(this.templateName, { lower: true, strict: true });
  }
  next();
});

const Template = mongoose.model("Template", templateSchema);

export default Template;
