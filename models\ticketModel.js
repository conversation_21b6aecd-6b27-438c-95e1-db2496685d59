import mongoose from "mongoose";
import { v4 as uuidv4 } from "uuid";

const ticketSchema = new mongoose.Schema(
  {
    name: {
      type: String,
      default: "Guest", // Default to <PERSON> if user is not logged in
    },
    email: {
      type: String,
      required: true,
      lowercase: true,
    },
    subject: {
      type: String,
      required: true,
    },
    inquiryType: {
      type: String,
      required: true,
    },
    description: {
      type: String,
      required: true,
    },
    attachments: {
      type: [String], // Store the image URL
      default: [],
    },
    createdAt: {
      type: Date,
      default: Date.now, // Automatically set to current date
    },
    status: {
      type: String,
      enum: ["open", "responded", "closed"],
      default: "open", // Default status is open
    },
    conversationId: {
      type: mongoose.Schema.Types.ObjectId,
      ref: "Conversation",
    },
    ticketNumber: {
      type: String,
      unique: true,
      default: function () {
        return `TICKET-${uuidv4().slice(0, 8).toUpperCase()}`;
      },
    },
    user: {
      type: mongoose.Schema.Types.ObjectId,
      ref: 'User',
      required: false, // Optional for guest tickets
    },
  },
  {
    timestamps: true, // Adds createdAt and updatedAt fields
  }
);

export default mongoose.model("Ticket", ticketSchema);
