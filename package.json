{"name": "vbrae-backend", "version": "1.0.0", "main": "index.js", "type": "module", "scripts": {"test": "echo \"Error: no test specified\" && exit 1", "start": "node index.js", "dev": "nodemon index.js"}, "author": "", "license": "ISC", "description": "", "dependencies": {"@sendgrid/mail": "^8.1.4", "aws-sdk": "^2.1346.0", "axios": "^1.2.1", "bcryptjs": "^2.4.3", "cors": "^2.8.5", "csv-parser": "^3.1.0", "dotenv": "^16.0.3", "ecommpay": "^0.1.7", "express": "^4.21.2", "express-async-handler": "^1.2.0", "express-fileupload": "^1.4.0", "fs": "^0.0.1-security", "http-status": "^1.8.0", "jsonwebtoken": "^9.0.2", "moment": "^2.30.1", "mongoose": "^6.10.0", "multer": "^1.4.5-lts.1", "node-cron": "^3.0.3", "nodemon": "^2.0.20", "pluralize": "^8.0.0", "pusher": "^5.2.0", "pusher-js": "^8.4.0", "slugify": "^1.6.6"}}