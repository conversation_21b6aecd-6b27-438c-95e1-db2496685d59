import express from "express";
import {
  createBanner,
  getBanner,
  getAllBanners,
  updateBanner,
  deleteBanner,
} from "./../controllers/bannerController.js";
import { protect, admin } from "../middleware/authMiddleware.js";

const router = express.Router();

// Public routes
router.route("/").get(getAllBanners);
router.route("/:id").get(getBanner);

// Protected routes (admin only)
router.use(protect, admin);
router.route("/").post(createBanner);
router.route("/:id").patch(updateBanner).delete(deleteBanner);

export default router;
