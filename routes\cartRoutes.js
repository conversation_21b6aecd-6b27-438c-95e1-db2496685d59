// routes/cartRoutes.js
import express from "express";
import {
  addToCart,
  updateCartItem,
  getCartSummary,
  createOrder,
  getPurchaseHistory,
  removeFromCart,
  applyCouponToCart,
} from "./../controllers/cartController.js";
import { protect } from "../middleware/authMiddleware.js";

const router = express.Router();

router.use(protect); // Protect all routes

router.route("/").post(addToCart).get(getCartSummary);
router.route("/update/:cartId").patch(updateCartItem);
router.route("/checkout").post(createOrder);
router.route("/history").get(getPurchaseHistory);
router.route("/removeItem/:cartId").delete(removeFromCart);
router.route("/attach-coupon").post(applyCouponToCart);
export default router;
