import express from "express";
import { admin, protect, seller } from "../middleware/authMiddleware.js";
import {
  getOffer,
  createOffer,
  getAllOffer,
  updateOffer,
  deleteOffer,
} from "./../controllers/offerController.js";

const router = express.Router();

// Route to create a new offer (admin only)
router.route("/").post(protect, seller, createOffer);
// Route to get a specific offer by ID (accessible by authenticated users)
router.route("/:id").get(getOffer);
// Route to get all offers (accessible by admin users)
router.route("/").get(getAllOffer);
// Route to update an offer by ID (admin only)
router.route("/:id").patch(protect, updateOffer);
// Route to delete an offer by ID (admin only)
router.route("/:id").delete(protect, deleteOffer);

export default router;
