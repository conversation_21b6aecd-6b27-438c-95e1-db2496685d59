import express from 'express';

const router = express.Router();
import {
    createNewRequest,
    getRequestById,
    getAllRequests,
    approveRequest,
    deleteRequest,
    rejectRequest,
} from './../controllers/requestController.js';
import { admin, protect, seller } from './../middleware/authMiddleware.js';


// Route to create a new request (only accessible by sellers)
router.route('/')
    .post(protect, seller, createNewRequest);
// Route to get request by ID (accessible by authenticated users)
router.route('/:id')
    .get(protect, getRequestById);
// Route to get all requests (admin only)
router.route('/')
    .get(protect, getAllRequests);
// Route to approve a request (admin only)
router.route('/:id/approve')
    .patch(protect, admin, approveRequest);
router.route('/:id/reject')
    .patch(protect, admin, rejectRequest);
// Route to delete a request (admin only)
router.route('/:id')
    .delete(protect, admin, deleteRequest);

export default router;
