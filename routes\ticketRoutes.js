import express from "express";
import {
  createTicket,
  getAllTickets,
  getTicketById,
  getUserTickets,
  updateTicketStatus,
  deleteTicket,
} from "../controllers/ticketController.js";
import { protect, admin } from "../middleware/authMiddleware.js";

const router = express.Router();

// Create a new ticket (any user)
router.route("/").post(createTicket).get(protect, getAllTickets);

// Get user's own tickets (authenticated users)
router.route("/my-tickets").get(protect, getUserTickets);

// Get, update status, or delete a ticket by ID (admin only for update & delete)
router
  .route("/:id")
  .get(protect, getTicketById)
  .patch(protect, updateTicketStatus)
  .delete(protect, admin, deleteTicket);

export default router;
