import express from 'express';
import {
  addToWatchlist,
  removeFromWatchlist,
  getWatchlist,
  toggleWatchlist,
  getWatchlistCount,
} from '../controllers/watchlistController.js';
import { protect } from '../middleware/authMiddleware.js';

const router = express.Router();

router.route("/").get(protect, getWatchlist).post(protect, addToWatchlist);
router.route("/toggle").post(protect, toggleWatchlist);
router.route("/count").get(protect, getWatchlistCount);
router.route("/:id").delete(protect, removeFromWatchlist);

export default router; 