import sendgrid from "@sendgrid/mail";
import NewsletterModel from "../../models/newsLetterModal.js";

export const subscriptionMail = async ({data, subject, html}) => {

    const isOffer = !!data.template;

    const subscribers = await NewsletterModel.find();

    const regionSubscribers = isOffer && data.region !== "Global" ? subscribers.filter(
        sub => sub.country === data.region
    ) : subscribers;

    const messages = regionSubscribers.map(sub => ({
        to: sub.email,
        from: process.env.EMAIL_FROM,
        subject,
        html
    }));

    // You can use `sendMultiple` (SendGrid supports batching)
    await sendgrid.send(messages);
};
